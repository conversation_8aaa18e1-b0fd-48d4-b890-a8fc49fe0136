/* CSS 自定义属性 - 颜色系统 */
:root {
  /* 主色调 */
  --color-primary: #3B82F6;
  --color-primary-hover: #2563EB;
  --color-primary-light: #DBEAFE;
  
  /* 辅助色 */
  --color-secondary: #6B7280;
  --color-secondary-hover: #4B5563;
  --color-secondary-light: #F3F4F6;
  
  /* 状态色 */
  --color-success: #10B981;
  --color-success-light: #D1FAE5;
  --color-warning: #F59E0B;
  --color-warning-light: #FEF3C7;
  --color-error: #EF4444;
  --color-error-light: #FEE2E2;
  --color-info: #06B6D4;
  --color-info-light: #CFFAFE;
  
  /* 中性色 */
  --color-white: #FFFFFF;
  --color-gray-50: #F9FAFB;
  --color-gray-100: #F3F4F6;
  --color-gray-200: #E5E7EB;
  --color-gray-300: #D1D5DB;
  --color-gray-400: #9CA3AF;
  --color-gray-500: #6B7280;
  --color-gray-600: #4B5563;
  --color-gray-700: #374151;
  --color-gray-800: #1F2937;
  --color-gray-900: #111827;
  
  /* 背景色 */
  --bg-primary: var(--color-white);
  --bg-secondary: var(--color-gray-50);
  --bg-tertiary: var(--color-gray-100);
  
  /* 文字色 */
  --text-primary: var(--color-gray-900);
  --text-secondary: var(--color-gray-600);
  --text-tertiary: var(--color-gray-400);
  
  /* 边框色 */
  --border-color: var(--color-gray-200);
  --border-hover: var(--color-gray-300);
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* 毛玻璃效果 */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-bg-dark: rgba(0, 0, 0, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-border-dark: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  --glass-blur: blur(16px);
  
  /* 紧凑化间距系统 - 大幅减少所有间距 */
  --spacing-1: 0.125rem;  /* 2px - 原0.25rem */
  --spacing-2: 0.25rem;   /* 4px - 原0.5rem */
  --spacing-3: 0.375rem;  /* 6px - 原0.75rem */
  --spacing-4: 0.5rem;    /* 8px - 原1rem */
  --spacing-5: 0.625rem;  /* 10px - 原1.25rem */
  --spacing-6: 0.75rem;   /* 12px - 原1.5rem */
  --spacing-8: 1rem;      /* 16px - 原2rem */
  --spacing-10: 1.25rem;  /* 20px - 原2.5rem */
  --spacing-12: 1.5rem;   /* 24px - 原3rem */
  
  /* 圆角 */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  
  /* 过渡动画 */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* 字体 */
  --font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  
  /* 行高 */
  --line-height-tight: 1.2;   /* 更紧凑的行高 */
  --line-height-normal: 1.4;  /* 减少行高 */
  --line-height-relaxed: 1.6; /* 减少行高 */
}

/* 暗色主题 */
[data-theme="dark"] {
  --bg-primary: var(--color-gray-900);
  --bg-secondary: var(--color-gray-800);
  --bg-tertiary: var(--color-gray-700);
  --text-primary: var(--color-gray-100);
  --text-secondary: var(--color-gray-300);
  --text-tertiary: var(--color-gray-500);
  --border-color: var(--color-gray-700);
  --border-hover: var(--color-gray-600);
}

/* 基础重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 布局容器 */
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-3) 0; /* 减少头部padding */
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-title {
  font-size: var(--font-size-xl);  /* 减少标题大小 */
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.title-icon {
  font-size: var(--font-size-2xl);  /* 减少图标大小 */
}

.header-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);  /* 减少控件间距 */
}

/* 持久化邮箱输入 */
.persistent-email {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-right: var(--spacing-4);
  padding: var(--spacing-1) var(--spacing-2);
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

.persistent-email label {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  white-space: nowrap;
}

.persistent-email input {
  border: none;
  background: transparent;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  padding: var(--spacing-1);
  min-width: 200px;
  outline: none;
}

.persistent-email input:focus {
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
}

.persistent-email input.valid {
  color: var(--color-success);
}

.persistent-email input.invalid {
  color: var(--color-error);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);  /* 减少用户信息间距 */
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.main-content {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-4) var(--spacing-4); /* 大幅减少主内容padding */
  width: 100%;
}

/* 登录面板 */
.login-panel {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.login-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-6); /* 减少登录卡片padding */
  width: 100%;
  max-width: 400px;
  border: 1px solid var(--border-color);
}

.login-card h2 {
  text-align: center;
  margin-bottom: var(--spacing-4); /* 减少标题下方间距 */
  color: var(--text-primary);
  font-size: var(--font-size-xl);
}

.login-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.login-actions .btn {
  width: 100%;
}

.login-actions .btn-sm {
  font-size: var(--font-size-sm);
  padding: var(--spacing-2) var(--spacing-3);
}

/* 工作区 */
.workspace {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4); /* 大幅减少工作区间距 */
}

/* 田字格布局容器 */
.grid-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: var(--spacing-4);
  height: 600px; /* 固定高度确保四个方块相等 */
  margin-bottom: var(--spacing-4);
}

/* 田字格项目 */
.grid-item {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  box-shadow: var(--glass-shadow);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 田字格项目头部 */
.grid-item .section-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

.grid-item .section-header h3 {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* 田字格项目内容 */
.grid-item .input-card,
.grid-item .form-card {
  flex: 1;
  padding: var(--spacing-3);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

/* 订单输入区域特殊样式 */
.input-section textarea {
  min-height: 100px;
  resize: vertical;
}

.input-section .input-actions {
  margin-top: auto;
  padding-top: var(--spacing-2);
  border-top: 1px solid var(--border-color);
}

/* 服务配置区域 */
.service-config-section {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  box-shadow: var(--glass-shadow);
  margin-bottom: var(--spacing-4);
}

.service-config-section .section-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
}

.service-config-section .section-header h3 {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.service-config-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
}

.config-left,
.config-right {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

/* 价格输入组 */
.price-input-group {
  display: flex;
  gap: var(--spacing-2);
}

.price-input-group input {
  flex: 1;
}

.price-input-group select {
  width: 80px;
  flex-shrink: 0;
}

/* 复选框组 */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

/* 操作按钮区域 */
.action-section {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  box-shadow: var(--glass-shadow);
  padding: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.action-section .form-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-4);
  margin: 0;
  padding: 0;
  border: none;
}

.action-section .btn {
  min-width: 150px;
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
  font-weight: 600;
}

/* 数据异常提示样式 */
.data-issues {
  color: var(--text-primary);
}

.issue-item {
  color: var(--color-warning);
  margin: var(--spacing-1) 0;
}

.issue-note {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-3);
  font-style: italic;
}

/* 区域样式 */
.input-section,
.preview-section,
.console-section {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.section-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-3); /* 减少区域头部padding */
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h3 {
  font-size: var(--font-size-base); /* 减少区域标题大小 */
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.section-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.input-card,
.preview-card,
.console-card {
  padding: var(--spacing-4); /* 大幅减少卡片内部padding */
}

/* 实时分析相关样式 */
.realtime-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.realtime-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  background: var(--color-primary-light);
  color: var(--color-primary);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
  width: fit-content;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: var(--spacing-3); /* 减少输入操作间距 */
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--border-color);
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.image-upload-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.image-upload-controls .image-upload-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-sm);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.image-upload-controls .image-upload-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.image-upload-controls .image-upload-button:hover::before {
  left: 100%;
}

.image-upload-controls .image-upload-button:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.image-upload-controls .upload-icon {
  font-size: 1.1rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* 表单样式 - 大幅紧凑化 */
.form-group {
  margin-bottom: var(--spacing-2); /* 从spacing-4减少到spacing-2 */
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-1); /* 减少标签下方间距 */
  font-weight: 500;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-2); /* 从spacing-3减少到spacing-2 */
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm); /* 减少字体大小 */
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-light); /* 减少焦点阴影 */
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: var(--color-error);
  box-shadow: 0 0 0 2px var(--color-error-light);
}

.field-error {
  color: var(--color-error);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px; /* 减少文本域最小高度 */
}

.form-section {
  margin-bottom: var(--spacing-4); /* 从spacing-8大幅减少到spacing-4 */
  padding-bottom: var(--spacing-3); /* 从spacing-6减少到spacing-3 */
  border-bottom: 1px solid var(--border-color);
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section h4 {
  font-size: var(--font-size-base); /* 减少表单区域标题大小 */
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-3); /* 减少标题下方间距 */
  padding-bottom: var(--spacing-1);
  border-bottom: 2px solid var(--color-primary);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 改为三列固定布局 */
  gap: var(--spacing-2); /* 进一步减少网格间距 */
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  margin-bottom: 0;
}

.checkbox-group input[type="checkbox"] {
  width: auto;
  margin: 0;
}

/* 复选框样式 */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  margin-bottom: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  user-select: none;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
  cursor: pointer;
}

.checkbox-text {
  cursor: pointer;
}

.checkbox-label:hover {
  color: var(--text-primary);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3); /* 减少按钮padding */
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--color-primary);
  color: var(--color-white);
  border-color: var(--color-primary);
}

.btn-primary:hover:not(:disabled) {
  background: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--color-secondary);
  color: var(--color-white);
  border-color: var(--color-secondary);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-secondary-hover);
  border-color: var(--color-secondary-hover);
}

.btn-outline {
  background: transparent;
  color: var(--text-primary);
  border-color: var(--border-color);
}

.btn-outline:hover:not(:disabled) {
  background: var(--bg-tertiary);
  border-color: var(--border-hover);
}

.btn-sm {
  padding: var(--spacing-1) var(--spacing-2); /* 减少小按钮padding */
  font-size: var(--font-size-xs);
}

.btn-icon {
  width: 32px;  /* 减少图标按钮大小 */
  height: 32px;
  padding: 0;
  border-radius: 50%;
}

.loading-spinner {
  font-size: var(--font-size-base);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 操作按钮组 */
.form-actions {
  display: flex;
  gap: var(--spacing-2); /* 减少按钮组间距 */
  margin-top: var(--spacing-4); /* 减少顶部间距 */
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--border-color);
}

/* 日志控制台 */
.log-console {
  background: var(--color-gray-900);
  color: var(--color-gray-100);
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  padding: var(--spacing-3); /* 减少控制台padding */
  border-radius: var(--radius-md);
  height: 250px; /* 减少控制台高度 */
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.log-entry {
  margin-bottom: var(--spacing-1); /* 减少日志条目间距 */
  padding: var(--spacing-1);
  border-radius: var(--radius-sm);
  border-left: 3px solid transparent;
}

.log-entry.info {
  background: rgba(6, 182, 212, 0.1);
  border-left-color: var(--color-info);
}

.log-entry.success {
  background: rgba(16, 185, 129, 0.1);
  border-left-color: var(--color-success);
}

.log-entry.warning {
  background: rgba(245, 158, 11, 0.1);
  border-left-color: var(--color-warning);
}

.log-entry.error {
  background: rgba(239, 68, 68, 0.1);
  border-left-color: var(--color-error);
}

.log-timestamp {
  color: var(--color-gray-400);
  font-size: var(--font-size-xs);
}

/* 切换开关 */
.toggle-switch {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.toggle-switch input {
  display: none;
}

.toggle-slider {
  width: 40px; /* 减少开关大小 */
  height: 20px;
  background: var(--color-gray-300);
  border-radius: 10px;
  position: relative;
  transition: background-color var(--transition-fast);
}

.toggle-slider::before {
  content: '';
  position: absolute;
  width: 16px; /* 减少开关按钮大小 */
  height: 16px;
  border-radius: 50%;
  background: var(--color-white);
  top: 2px;
  left: 2px;
  transition: transform var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.toggle-switch input:checked + .toggle-slider {
  background: var(--color-primary);
}

.toggle-switch input:checked + .toggle-slider::before {
  transform: translateX(20px);
}

/* 状态栏 */
.status-bar {
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-1) var(--spacing-4); /* 减少状态栏padding */
  font-size: var(--font-size-xs);
}

.status-info {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  gap: var(--spacing-3);
}

.status-item {
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

/* 模态框 - 重点优化，确保订单预览无滚动显示 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3000; /* 提高z-index确保在预览浮窗之上 */
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  padding: var(--spacing-4); /* 添加模态框外边距 */
}

.modal-content {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-blur);
  backdrop-filter: var(--glass-blur);
  border-radius: var(--radius-lg);
  box-shadow: var(--glass-shadow);
  border: 1px solid var(--glass-border);
  width: 100%;
  max-width: 900px; /* 增加最大宽度 */
  max-height: 90vh; /* 减少最大高度，确保有边距 */
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-3); /* 减少模态框头部padding */
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.modal-header h3 {
  font-size: var(--font-size-base); /* 减少模态框标题大小 */
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-3); /* 大幅减少模态框body padding */
  overflow-y: auto;
  flex: 1;
  min-height: 0;
}

.modal-footer {
  background: var(--bg-tertiary);
  padding: var(--spacing-3); /* 减少模态框footer padding */
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-2); /* 减少按钮间距 */
  flex-shrink: 0;
}

/* 订单预览专用样式 - 极致紧凑化 */
.order-preview {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-tight);
}

.order-preview h4 {
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-2);
  color: var(--color-primary);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: var(--spacing-1);
}

.order-preview h5 {
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-2);
  color: var(--text-primary);
  font-weight: 600;
}

.order-preview p {
  margin: 0 0 var(--spacing-1) 0; /* 极致减少段落间距 */
  line-height: var(--line-height-tight);
}

/* 这些样式已移动到浮窗样式部分，避免重复 */



/* API结果显示 */
.api-result-block {
  margin-top: var(--spacing-3);
}

.api-result-json {
  background: #222 !important;
  color: #fff !important;
  padding: var(--spacing-2) !important;
  border-radius: var(--radius-md) !important;
  max-height: 200px !important; /* 限制高度 */
  overflow: auto !important;
  font-size: var(--font-size-xs) !important;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
}

/* 成功/错误消息样式 */
.success-message,
.error-message {
  margin-bottom: var(--spacing-3);
}

.success-message p,
.error-message p {
  margin-bottom: var(--spacing-1);
}

/* 订单成功样式 */
.order-success-header {
  text-align: center;
  margin-bottom: var(--spacing-3);
  padding-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
}

.order-success-header h4 {
  color: var(--color-success);
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-lg);
}

.order-id-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  margin-top: var(--spacing-2);
}

.order-id-highlight {
  color: var(--color-primary);
  font-size: var(--font-size-lg);
  font-family: 'Courier New', monospace;
  background: var(--color-primary-light);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-primary);
}

.order-details {
  background: var(--bg-secondary);
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  margin-top: var(--spacing-3);
}

.order-details p {
  margin: var(--spacing-1) 0;
  font-size: var(--font-size-sm);
}

/* 响应式设计 - 大幅优化 */
@media (max-width: 1024px) {
  .main-content {
    max-width: 100vw;
    padding: var(--spacing-3) var(--spacing-2);
  }
  
  .input-card {
    padding: var(--spacing-3);
  }
  
  .form-section {
    margin-bottom: var(--spacing-3);
    padding-bottom: var(--spacing-2);
  }
  
  .form-group {
    margin-bottom: var(--spacing-2);
  }
  
  .form-actions,
  .input-actions {
    margin-top: var(--spacing-3);
    padding-top: var(--spacing-2);
    gap: var(--spacing-2);
  }
  
  .workspace {
    gap: var(--spacing-3);
  }

  /* 田字格在平板上改为2x2布局，但高度自适应 */
  .grid-container {
    height: auto;
    grid-template-rows: auto auto;
  }

  .grid-item {
    min-height: 300px;
  }

  /* 服务配置区域在平板上保持左右布局 */
  .service-config-container {
    gap: var(--spacing-3);
  }

  .modal-content {
    max-width: 95vw;
  }


}

@media (max-width: 768px) {
  html {
    font-size: 15px;
  }
  
  .header-content {
    flex-direction: column;
    gap: var(--spacing-2);
    padding: 0 var(--spacing-2);
  }
  
  .app-title {
    font-size: var(--font-size-lg);
  }
  
  .main-content {
    padding: var(--spacing-2) var(--spacing-1);
  }

  /* 田字格在手机上保持2x2布局，但高度适应内容 */
  .grid-container {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    height: auto;
    gap: var(--spacing-2);
  }

  .grid-item {
    min-height: 200px;
  }

  /* 服务配置区域在手机上改为单列布局，但保持简洁 */
  .service-config-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-2);
  }

  .config-left,
  .config-right {
    min-height: auto;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-2);
  }
  
  .section-header {
    flex-direction: column;
    gap: var(--spacing-2);
    align-items: flex-start;
    padding: var(--spacing-2);
  }
  
  .section-controls {
    width: 100%;
    justify-content: flex-end;
    flex-wrap: wrap;
    gap: var(--spacing-1);
  }
  
  .section-controls .btn {
    font-size: var(--font-size-xs);
    padding: var(--spacing-1) var(--spacing-2);
  }
  
  .form-actions,
  .input-actions {
    flex-direction: column;
    gap: var(--spacing-2);
  }
  
  .input-actions {
    align-items: stretch;
  }

  .image-upload-controls {
    justify-content: center;
  }

  .image-upload-controls .image-upload-button {
    padding: var(--spacing-3) var(--spacing-4);
    min-width: 150px;
    justify-content: center;
  }
  
  /* 特殊要求区域移动端样式 */
  .requirements-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
  }

  .checkbox-group-vertical {
    gap: var(--spacing-2);
  }
  
  .login-card {
    margin: var(--spacing-2);
    padding: var(--spacing-4);
  }
  
  .modal {
    padding: var(--spacing-2);
  }

  .modal-content {
    width: 100%;
    max-width: 100vw;
    max-height: 95vh;
  }
  
  .modal-body {
    padding: var(--spacing-2);
  }

  .modal-header,
  .modal-footer {
    padding: var(--spacing-2);
  }

  /* 田字格内部内容的移动端优化 */
  .grid-item .section-header h3 {
    font-size: var(--font-size-sm);
  }
  
  .grid-item .input-card,
  .grid-item .form-card {
    padding: var(--spacing-2);
    gap: var(--spacing-1);
  }
  
  .grid-item .form-group {
    margin-bottom: var(--spacing-1);
  }
  
  .grid-item .form-group label {
    font-size: var(--font-size-xs);
    margin-bottom: var(--spacing-1);
  }
  
  .grid-item .form-group input,
  .grid-item .form-group select,
  .grid-item .form-group textarea {
    font-size: var(--font-size-sm);
    padding: var(--spacing-1) var(--spacing-2);
  }

  /* 图片上传区域移动端优化 */
  .image-upload-area {
    min-height: 60px;
  }
  
  .upload-icon {
    font-size: var(--font-size-lg);
  }
  
  .upload-primary {
    font-size: var(--font-size-xs);
  }
  
  .upload-secondary {
    font-size: 10px;
  }

  /* 订单输入区域移动端优化 */
  .input-section textarea {
    min-height: 80px;
    font-size: var(--font-size-sm);
  }
  
  .realtime-badge {
    font-size: 10px;
    padding: var(--spacing-1);
  }


}

@media (max-width: 480px) {
  html {
    font-size: 14px;
  }
  
  .main-content {
    padding: var(--spacing-1);
  }
  
  /* 超小屏幕仍保持田字格布局，但进一步紧凑化 */
  .grid-container {
    gap: var(--spacing-1);
  }

  .grid-item {
    min-height: 180px;
  }
  
  .grid-item .section-header {
    padding: var(--spacing-1) var(--spacing-2);
  }
  
  .grid-item .section-header h3 {
    font-size: 12px;
  }
  
  .grid-item .input-card,
  .grid-item .form-card {
    padding: var(--spacing-1);
    gap: var(--spacing-1);
  }
  
  .form-section {
    margin-bottom: var(--spacing-1);
    padding-bottom: var(--spacing-1);
  }
  
  .form-group {
    margin-bottom: var(--spacing-1);
  }
  
  .form-group label {
    font-size: 11px;
    margin-bottom: 2px;
  }
  
  .form-group input,
  .form-group select,
  .form-group textarea {
    font-size: 12px;
    padding: var(--spacing-1);
  }
  
  .form-actions,
  .input-actions {
    margin-top: var(--spacing-1);
    padding-top: var(--spacing-1);
    gap: var(--spacing-1);
  }
  
  .workspace {
    gap: var(--spacing-1);
  }

  .section-controls .btn {
    font-size: 10px;
    padding: var(--spacing-1);
  }

  /* 图片上传区域超小屏优化 */
  .image-upload-area {
    min-height: 50px;
  }
  
  .upload-icon {
    font-size: var(--font-size-base);
  }
  
  .upload-primary {
    font-size: 10px;
  }
  
  .upload-secondary {
    font-size: 8px;
  }

  /* 文本区域超小屏优化 */
  .input-section textarea {
    min-height: 60px;
    font-size: 12px;
  }
  
  .realtime-badge {
    font-size: 8px;
    padding: 2px var(--spacing-1);
  }

  .realtime-info small {
    font-size: 8px;
  }

  .modal {
    padding: var(--spacing-1);
  }
  
  .modal-content {
    width: 100%;
    max-width: 100vw;
    max-height: 98vh;
    border-radius: var(--radius-md);
  }
  
  .modal-body {
    padding: var(--spacing-2);
  }

  .modal-header,
  .modal-footer {
    padding: var(--spacing-2);
  }

  .section-header {
    padding: var(--spacing-2);
  }

  .preview-section p strong {
    min-width: 60px;
    font-size: var(--font-size-xs);
  }

  .api-result-json {
    max-height: 150px !important;
  }

  /* 极小屏幕的特殊优化 */
  .form-section h4 {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-2);
  }

  .order-preview h5 {
    font-size: var(--font-size-xs);
    margin-bottom: var(--spacing-1);
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px); /* 减少动画距离 */
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.fade-in {
  animation: fadeIn var(--transition-normal) ease-out;
}

.slide-in {
  animation: slideIn var(--transition-normal) ease-out;
}

/* 实用类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none !important; }
.visible { display: block !important; }
.show { display: block !important; }
.show.flex { display: flex !important; }
.hidden-field { display: none !important; }

/* OTA渠道输入组合样式 */
.ota-channel-inputs {
  display: flex;
  gap: var(--spacing-2);
  flex: 1;
}

.ota-channel-inputs select,
.ota-channel-inputs input {
  flex: 1;
}

.mt-4 { margin-top: var(--spacing-4); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mr-4 { margin-right: var(--spacing-4); }
.ml-4 { margin-left: var(--spacing-4); }

.p-4 { padding: var(--spacing-4); }
.pt-4 { padding-top: var(--spacing-4); }
.pb-4 { padding-bottom: var(--spacing-4); }
.pr-4 { padding-right: var(--spacing-4); }
.pl-4 { padding-left: var(--spacing-4); }

/* 预览卡片额外优化 */
.preview-card {
  padding: var(--spacing-3);
}

/* 紧凑模式的额外CSS变量 */
:root {
  --compact-spacing: var(--spacing-1);
  --compact-gap: var(--spacing-2);
}



/* ========================================
   可编辑字段样式
   ======================================== */

/* 字段容器 */
.field-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* 编辑按钮 */
.edit-field-btn {
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-1);
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  flex-shrink: 0;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
}

.edit-field-btn:hover {
  background: var(--bg-tertiary);
  border-color: var(--color-primary);
  color: var(--color-primary);
  opacity: 1;
  transform: scale(1.05);
}

.edit-field-btn:active {
  transform: scale(0.95);
}

/* 字段编辑状态 */
.editable-field.editing .edit-field-btn {
  background: var(--color-primary-light);
  border-color: var(--color-primary);
  color: var(--color-primary);
  opacity: 1;
}

.editable-field.editing input,
.editable-field.editing select,
.editable-field.editing textarea {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-light);
  background: var(--color-primary-light);
}

/* 字段保存和取消按钮 */
.field-actions {
  display: none;
  gap: var(--spacing-1);
  margin-top: var(--spacing-1);
}

.editable-field.editing .field-actions {
  display: flex;
}

.field-action-btn {
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-1) var(--spacing-2);
  cursor: pointer;
  font-size: var(--font-size-xs);
  transition: all var(--transition-fast);
}

.field-action-btn.save {
  color: var(--color-success);
  border-color: var(--color-success);
}

.field-action-btn.save:hover {
  background: var(--color-success);
  color: white;
}

.field-action-btn.cancel {
  color: var(--color-error);
  border-color: var(--color-error);
}

.field-action-btn.cancel:hover {
  background: var(--color-error);
  color: white;
}



/* ========================================
   历史订单面板样式
   ======================================== */

/* 历史订单面板 */
.history-panel {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2500;
}

.history-panel.show {
  display: block;
}

/* 历史订单遮罩 */
.history-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2501;
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  padding: var(--spacing-4);
}

/* 历史订单内容 */
.history-content {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-blur);
  backdrop-filter: var(--glass-blur);
  border-radius: var(--radius-lg);
  box-shadow: var(--glass-shadow);
  border: 1px solid var(--glass-border);
  width: 100%;
  max-width: 1200px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: historySlideIn 0.3s ease-out;
}

/* 历史订单头部 */
.history-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.history-header h3 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin: 0;
}

.history-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* 搜索区域 */
.history-search {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.search-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
}

.search-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.search-group label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.search-group input {
  padding: var(--spacing-2);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
}

.search-actions {
  display: flex;
  gap: var(--spacing-2);
  justify-content: flex-end;
}

/* 统计区域 */
.history-stats {
  padding: var(--spacing-3) var(--spacing-4);
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-3);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-2);
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

.stat-label {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-1);
}

.stat-value {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-primary);
}

/* 订单列表 */
.history-list {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.list-header {
  padding: var(--spacing-3) var(--spacing-4);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-title {
  font-weight: 600;
  color: var(--text-primary);
}

.list-count {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.list-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-2);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-2);
  opacity: 0.5;
}

.empty-text {
  font-size: var(--font-size-base);
}

/* 订单项 */
.history-item {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  margin-bottom: var(--spacing-2);
  transition: all var(--transition-fast);
}

.history-item:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.history-item-id {
  font-weight: 600;
  color: var(--color-primary);
  font-family: monospace;
}

.history-item-time {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.history-item-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
}

.history-item-field {
  display: flex;
  flex-direction: column;
}

.history-item-label {
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
  margin-bottom: 2px;
}

.history-item-value {
  color: var(--text-primary);
  font-weight: 500;
}

.history-item-actions {
  margin-top: var(--spacing-2);
  display: flex;
  gap: var(--spacing-2);
  justify-content: flex-end;
}

/* 动画 */
@keyframes historySlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* ========================================
   图片上传组件样式
   ======================================== */

/* 图片上传区域 */
.image-upload-section {
  margin-bottom: var(--spacing-4);
}

/* 图片上传区域 - 已移除独立区域，改为按钮形式 */
.image-upload-section {
  margin-bottom: var(--spacing-4);
}

/* 通用图片上传按钮样式 */
.image-upload-button {
  background: var(--color-secondary);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-md);
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  box-shadow: var(--shadow-sm);
}

.image-upload-button:hover {
  background: var(--color-secondary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.image-upload-button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.image-upload-button:disabled {
  background: var(--color-gray-300);
  cursor: not-allowed;
  transform: none;
}

.upload-hint {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-align: center;
  margin-top: var(--spacing-2);
}

.upload-content {
  display: none; /* 不再使用的旧样式 */
}

.upload-icon {
  font-size: 1rem;
}

.upload-text {
  display: none; /* 隐藏原有的文本提示 */
}

.upload-primary {
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--text-primary);
}

.upload-secondary {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 上传状态 */
.upload-status {
  margin-top: var(--spacing-2);
  padding: var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  text-align: center;
  transition: all var(--transition-fast);
}

.upload-status.info {
  background: var(--color-info-light);
  color: var(--color-info);
  border: 1px solid var(--color-info);
}

.upload-status.success {
  background: var(--color-success-light);
  color: var(--color-success);
  border: 1px solid var(--color-success);
}

.upload-status.warning {
  background: var(--color-warning-light);
  color: var(--color-warning);
  border: 1px solid var(--color-warning);
}

.upload-status.error {
  background: var(--color-error-light);
  color: var(--color-error);
  border: 1px solid var(--color-error);
}

/* 图片预览容器 */
.image-preview-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-3);
  margin-top: var(--spacing-3);
}

.image-preview-item {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  overflow: hidden;
  transition: all var(--transition-fast);
}

.image-preview-item:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

.image-preview-wrapper {
  position: relative;
  aspect-ratio: 16/9;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-fast);
}

.image-preview-item:hover .preview-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0) 30%,
    rgba(0, 0, 0, 0) 70%,
    rgba(0, 0, 0, 0.3) 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: var(--spacing-2);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.image-preview-item:hover .image-overlay {
  opacity: 1;
}

.image-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  align-self: flex-start;
}

.image-status[data-status="analyzing"] {
  background: rgba(59, 130, 246, 0.8);
}

.image-status[data-status="analyzed"] {
  background: rgba(34, 197, 94, 0.8);
}

.image-status[data-status="error"] {
  background: rgba(239, 68, 68, 0.8);
}

.image-actions {
  display: flex;
  gap: var(--spacing-1);
  align-self: flex-end;
}

.delete-image-btn {
  background: rgba(239, 68, 68, 0.8);
  color: white;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.delete-image-btn:hover {
  background: rgba(239, 68, 68, 1);
  transform: scale(1.1);
}

.image-info {
  padding: var(--spacing-2);
}

.image-name {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.image-size {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-preview-container {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--spacing-2);
  }

  .image-upload-area {
    gap: var(--spacing-2);
  }

  .image-upload-button {
    padding: var(--spacing-3) var(--spacing-5);
    min-width: 180px;
    font-size: var(--font-size-sm);
  }

  .upload-icon {
    font-size: 1rem;
  }

  .upload-hint {
    font-size: var(--font-size-xs);
  }
}

/* ========================================
   价格转换组件样式
   ======================================== */

/* 价格转换提示 */
.price-conversion-note {
  margin-top: var(--spacing-2);
  padding: var(--spacing-2);
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-blur);
  backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
}

.conversion-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--color-info);
}

.conversion-icon {
  font-size: var(--font-size-base);
  opacity: 0.8;
}

.conversion-text {
  font-weight: 500;
}

/* 汇率设置面板 */
.exchange-rate-panel {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-4);
  margin-top: var(--spacing-3);
}

.exchange-rate-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-3);
}

.exchange-rate-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.exchange-rate-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-3);
}

.rate-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.rate-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.rate-input {
  padding: var(--spacing-2);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.rate-input:focus {
  border-color: var(--color-primary);
  outline: none;
  box-shadow: 0 0 0 2px var(--color-primary-light);
}

.rate-actions {
  display: flex;
  gap: var(--spacing-2);
  margin-top: var(--spacing-3);
  justify-content: flex-end;
}

/* 价格显示增强 */
.price-display {
  position: relative;
}

.price-display.has-conversion::after {
  content: "💱";
  position: absolute;
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: var(--font-size-sm);
  opacity: 0.6;
}

/* ========================================
   多订单预览面板样式
   ======================================== */

/* 多订单面板 */
.multi-order-panel {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2600;
}

.multi-order-panel.show {
  display: block;
}

/* 多订单遮罩 */
.multi-order-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2601;
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  padding: var(--spacing-4);
}

/* 多订单内容 */
.multi-order-content {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-blur);
  backdrop-filter: var(--glass-blur);
  border-radius: var(--radius-lg);
  box-shadow: var(--glass-shadow);
  border: 1px solid var(--glass-border);
  width: 100%;
  max-width: 1400px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: multiOrderSlideIn 0.3s ease-out;
}

/* 多订单头部 */
.multi-order-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.multi-order-header h3 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin: 0;
}

.multi-order-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.order-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-1);
}

.order-count {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--color-primary);
}

.date-range {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* 多订单列表 */
.multi-order-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-3);
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-3);
  align-content: start;
}

/* 多订单项 */
.multi-order-item {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  transition: all var(--transition-fast);
  position: relative;
}

.multi-order-item:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

.multi-order-item.selected {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.multi-order-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.order-sequence {
  background: var(--color-primary);
  color: white;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  font-weight: 600;
}

.order-actions {
  display: flex;
  gap: var(--spacing-1);
}

.multi-order-item-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
}

.order-field {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.order-field-label {
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.order-field-value {
  color: var(--text-primary);
  font-weight: 500;
}

.order-field-value.empty {
  color: var(--text-secondary);
  font-style: italic;
}

/* 批量创建进度显示 */
.batch-create-status {
  padding: var(--spacing-3) var(--spacing-4);
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-color);
  display: none;
  animation: slideDown 0.3s ease-out;
}

.batch-create-status:not(:empty) {
  display: block;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.progress-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.progress-icon {
  font-size: var(--font-size-lg);
  animation: spin 2s linear infinite;
}

.progress-label {
  font-weight: 600;
  color: var(--text-primary);
}

.progress-actions {
  display: flex;
  gap: var(--spacing-1);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
}

.progress-text {
  color: var(--text-primary);
  font-weight: 500;
}

.progress-percent {
  color: var(--color-primary);
  font-weight: 600;
  font-size: var(--font-size-base);
}

.progress-bar {
  width: 100%;
  height: 10px;
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
  overflow: hidden;
  margin-bottom: var(--spacing-2);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-success));
  border-radius: var(--radius-sm);
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.progress-count {
  font-weight: 500;
}

.progress-eta {
  font-style: italic;
}

/* 进度动画 */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 验证结果样式 */
.validation-results {
  max-height: 60vh;
  overflow-y: auto;
}

.validation-summary {
  display: flex;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
  padding: var(--spacing-2);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
}

.valid-count {
  color: var(--color-success);
  font-weight: 600;
}

.invalid-count {
  color: var(--color-error);
  font-weight: 600;
}

.invalid-orders {
  margin-bottom: var(--spacing-3);
}

.invalid-order-item {
  background: var(--bg-secondary);
  border: 1px solid var(--color-error-light);
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  margin-bottom: var(--spacing-2);
}

.invalid-order-item .order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.validation-errors {
  font-size: var(--font-size-sm);
}

.error-item {
  margin-bottom: var(--spacing-1);
}

.error-field {
  font-weight: 600;
  color: var(--color-error);
}

.error-messages {
  color: var(--text-secondary);
  margin-left: var(--spacing-1);
}

.valid-orders {
  margin-top: var(--spacing-3);
}

.valid-order-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-1);
}

.valid-order-tag {
  background: var(--color-success-light);
  color: var(--color-success);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

/* 确认对话框样式 */
.confirm-dialog {
  text-align: center;
  padding: var(--spacing-3);
}

.confirm-dialog p {
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-base);
  color: var(--text-primary);
}

.confirm-actions {
  display: flex;
  gap: var(--spacing-2);
  justify-content: center;
}

/* 结果显示样式 */
.success-message,
.error-message,
.mixed-results {
  text-align: center;
  padding: var(--spacing-3);
}

.success-icon,
.error-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-2);
}

/* 改进的结果显示 */
.result-header {
  text-align: center;
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
}

.result-summary {
  display: flex;
  gap: var(--spacing-2);
  justify-content: center;
  margin-top: var(--spacing-3);
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  min-width: 80px;
}

.summary-item.success {
  background: var(--color-success-light);
  border: 1px solid var(--color-success);
}

.summary-item.failure {
  background: var(--color-error-light);
  border: 1px solid var(--color-error);
}

.summary-item.total {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
}

.summary-icon {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-1);
}

.summary-label {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-1);
}

.summary-count {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.success-section,
.failure-section {
  text-align: left;
  margin-bottom: var(--spacing-3);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
}







.success-section {
  background: var(--color-success-light);
  border: 1px solid var(--color-success);
}

.failure-section {
  background: var(--color-error-light);
  border: 1px solid var(--color-error);
}

.success-section h5,
.failure-section h5 {
  margin-bottom: var(--spacing-3);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.success-list,
.failure-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.success-item,
.failure-item {
  background: var(--bg-primary);
  padding: var(--spacing-2);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

.success-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.failure-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.order-label {
  font-weight: 600;
   color: var(--text-primary);
}

.order-id {
  font-family: 'Courier New', monospace;
  background: var(--bg-secondary);
  padding: var(--spacing-1);
  border-radius: var(--radius-xs);
  font-size: var(--font-size-sm);
}

.failure-error {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-2);
}

.error-icon {
  font-size: var(--font-size-sm);
  margin-top: 2px;
}

.error-message {
  color: var(--color-error);
  font-size: var(--font-size-sm);
  line-height: 1.4;
}

.failure-suggestion {
  background: var(--bg-secondary);
  padding: var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  border-left: 3px solid var(--color-warning);
}

.result-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-2);
  margin-top: var(--spacing-4);
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--border-color);
}

/* 多订单底部 */
.multi-order-footer {
  background: var(--bg-secondary);
  padding: var(--spacing-3) var(--spacing-4);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.batch-actions {
  display: flex;
  gap: var(--spacing-2);
}

.creation-summary {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.creation-summary span {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 选择框 */
.order-checkbox {
  position: absolute;
  top: var(--spacing-2);
  left: var(--spacing-2);
  width: 18px;
  height: 18px;
  cursor: pointer;
}

/* 动画 */
@keyframes multiOrderSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .multi-order-list {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .multi-order-list {
    grid-template-columns: 1fr;
    gap: var(--spacing-2);
  }

  .multi-order-item-content {
    grid-template-columns: 1fr;
  }

  .multi-order-controls {
    flex-direction: column;
    align-items: flex-end;
    gap: var(--spacing-2);
  }

  .multi-order-footer {
    flex-direction: column;
    gap: var(--spacing-3);
  }
}

/* 子区域标题样式 */
.sub-section {
  margin-top: var(--spacing-4);
  margin-bottom: var(--spacing-2);
  border-top: 1px solid var(--border-color);
  padding-top: var(--spacing-3);
}

.sub-section h4 {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* 特殊要求区域样式 */
.requirements-section {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  -webkit-backdrop-filter: var(--glass-blur);
  backdrop-filter: var(--glass-blur);
  box-shadow: var(--glass-shadow);
  margin-bottom: var(--spacing-4);
}

.requirements-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
}

.requirements-left,
.requirements-right {
  display: flex;
  flex-direction: column;
}

.checkbox-group-vertical {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.checkbox-group-vertical .checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  margin-bottom: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  padding: var(--spacing-2);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.checkbox-group-vertical .checkbox-label:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

